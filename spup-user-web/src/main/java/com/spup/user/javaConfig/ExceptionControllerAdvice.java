package com.spup.user.javaConfig;

import com.spup.commons.api.CommonResult;
import com.spup.core.config.BaseExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@RestControllerAdvice(basePackages = "com.spup")
public class ExceptionControllerAdvice extends BaseExceptionHandler {

    /**
     * Handle user-specific exceptions
     */
    @Override
    protected CommonResult<?> handleModuleSpecificException(Exception e,
                                                           HttpServletRequest request,
                                                           HttpServletResponse response) {
        // User-specific exception handling logic
        log.error("User-specific exception handling for: {}", e.getClass().getSimpleName());

        // Add user-specific exception types here
        if (e instanceof IllegalStateException) {
            return CommonResult.failed("User state error: " + e.getMessage());
        }

        // Default user error response
        return CommonResult.failed("User system error");
    }

}
