package com.spup.user.javaConfig;

import org.springframework.format.annotation.DateTimeFormat;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AppointmentConfigDetail {
    List<DayOfWeek> weekdayList;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    List<Date> holidayList;

    public List<DayOfWeek> getWeekdayList() {
        return weekdayList;
    }

    public void setWeekdayList(List<DayOfWeek> weekdayList) {
        this.weekdayList = weekdayList;
    }

    public List<LocalDate> getHolidayList() {
        List<LocalDate> holidays = new ArrayList<>();
        for (Date date : holidayList) {
            holidays.add(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
        }
        return holidays;
    }

    public void setHolidayList(List<Date> holidayList) {
        this.holidayList = holidayList;
    }
}
