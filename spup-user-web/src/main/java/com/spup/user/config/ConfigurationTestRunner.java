package com.spup.user.config;

import com.spup.user.dto.Supplier;
import com.spup.user.javaConfig.AppointmentConfig;
import com.spup.user.javaConfig.AppointmentConfigDetail;
import com.spup.user.javaConfig.SupplierConfig;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;

@Component
@Profile("dev") // Only run in dev profile
public class ConfigurationTestRunner implements CommandLineRunner {

    @Resource
    private TurnstilesProperties turnstilesProperties;

    @Resource
    private SupplierConfig supplierConfig;

    @Resource
    private AppointmentConfig appointmentConfig;

    @Override
    public void run(String... args) throws Exception {
        System.out.println("\n\n=== Testing Configuration in Application ===");

        // Test TurnstilesProperties
        System.out.println("TurnstilesProperties suppliers:");
        List<TurnstilesProperties.Supplier> suppliers1 = turnstilesProperties.getSuppliers();
        if (suppliers1 != null) {
            for (TurnstilesProperties.Supplier supplier : suppliers1) {
                System.out.println("  - Name: " + supplier.getName());
                System.out.println("    SignKey: " + supplier.getSignKey());
            }
        } else {
            System.out.println("  No suppliers found in TurnstilesProperties");
        }

        // Test SupplierConfig
        System.out.println("\nSupplierConfig suppliers:");
        List<Supplier> suppliers2 = supplierConfig.getSuppliers();
        if (suppliers2 != null) {
            for (Supplier supplier : suppliers2) {
                System.out.println("  - Name: " + supplier.getName());
                System.out.println("    SignKey: " + supplier.getSignKey());
            }
        } else {
            System.out.println("  No suppliers found in SupplierConfig");
        }

        // Test AppointmentConfig
        System.out.println("\nAppointmentConfig:");
        if (appointmentConfig != null) {
            // Test 'all' configuration
            AppointmentConfigDetail allConfig = appointmentConfig.getConfig("all");
            if (allConfig != null) {
                System.out.println("  Found 'all' configuration");

                // Print weekdays
                List<DayOfWeek> weekdays = allConfig.getWeekdayList();
                if (weekdays != null && !weekdays.isEmpty()) {
                    System.out.println("  Weekdays: " + weekdays);
                } else {
                    System.out.println("  No weekdays configured");
                }

                // Print holidays
                List<LocalDate> holidays = allConfig.getHolidayList();
                if (holidays != null && !holidays.isEmpty()) {
                    System.out.println("  Holidays: " + holidays);
                } else {
                    System.out.println("  No holidays configured");
                }
            } else {
                System.out.println("  No 'all' configuration found");
            }

            // Test other configurations
            String[] configKeys = {"team", "fypd", "L20230906T20230915", "L20240716T20241231"};
            for (String key : configKeys) {
                AppointmentConfigDetail config = appointmentConfig.getConfig(key);
                if (config != null) {
                    System.out.println("  Found '" + key + "' configuration");
                } else {
                    System.out.println("  No '" + key + "' configuration found");
                }
            }
        } else {
            System.out.println("  AppointmentConfig is null");
        }

        System.out.println("=== End of Configuration Test ===\n\n");
    }
}
