package com.spup.user.service.impl;

import com.spup.data.dao.AppSurroundingGoodsDao;
import com.spup.data.entity.AppSurroundingGoods;
import com.spup.user.dto.GoodsListParam;
import com.spup.user.service.IAppSurroundingGoodsService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class AppSurroundingGoodsServiceImpl implements IAppSurroundingGoodsService {
    @Resource
    private AppSurroundingGoodsDao appSurroundingGoodsDao;
    @Override
    public Page<AppSurroundingGoods> getListByPage(GoodsListParam listParam) {
        //调用分页插件
        Pageable pageable = PageRequest.of(listParam.getPageNum()-1, listParam.getPageSize());
        //从数据库查询
        Specification<AppSurroundingGoods> spec = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            if(listParam.getGoodsName() != null && !listParam.getGoodsName().isEmpty()){
                list.add(cb.like(root.get("goodsName"),listParam.getGoodsName()));//筛选
            }
            if(listParam.getGoodsCategory() != null && !listParam.getGoodsCategory().isEmpty()){
                list.add(cb.equal(root.get("goodsCategory"),listParam.getGoodsCategory()));//筛选
            }
            if(listParam.getGoodsStatus()!=null){
                list.add(cb.equal(root.get("goodsStatus"),listParam.getGoodsStatus()));//筛选
            }
            query.orderBy(cb.desc(root.get("id")));//排序
            Predicate[] arr = new Predicate[list.size()];
            return cb.and(list.toArray(arr));
        };

        Page<AppSurroundingGoods> all = appSurroundingGoodsDao.findAll(spec, pageable);

        return all;
    }

    @Override
    public AppSurroundingGoods view(long id) {
        Optional<AppSurroundingGoods> byId = appSurroundingGoodsDao.findById(id);
        if(!byId.isPresent()){
            return null;
        }
        return byId.get();
    }
}
