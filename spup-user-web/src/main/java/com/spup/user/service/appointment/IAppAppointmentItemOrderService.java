package com.spup.user.service.appointment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.spup.commons.api.CommonResult;
import com.spup.user.dto.AppAppointmentItemOrderRequest;
import com.spup.user.service.IOrderService;


public interface IAppAppointmentItemOrderService extends IOrderService {
    //对外接口
    public CommonResult<?> save(AppAppointmentItemOrderRequest orderRequest, String unionid) throws JsonProcessingException;
    public CommonResult<?> getList(String unionid);
    public CommonResult<?> cancel(String orderNo,String unionid);
    public CommonResult<?> delete(String orderNo,String unionid);
    public CommonResult<?> breaked(String orderNo,String unionid);

}
