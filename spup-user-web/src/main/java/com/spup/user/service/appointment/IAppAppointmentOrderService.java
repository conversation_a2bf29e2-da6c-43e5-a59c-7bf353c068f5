package com.spup.user.service.appointment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.spup.commons.api.CommonResult;
import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.user.dto.AppAppointmentOrderRequest;
import com.spup.user.service.IOrderService;

import java.util.List;


public interface IAppAppointmentOrderService extends IOrderService {
    //对外接口
    CommonResult<?> save(AppAppointmentOrderRequest orderRequest, String unionid) throws JsonProcessingException;

    CommonResult<?> getList(String unionid);

    CommonResult<?> cancel(String orderNo, String unionid);

    CommonResult<?> breaked(String orderNo, String unionid);

    CommonResult<?> delete(String orderNo);

    //内部接口
    List<AppAppointmentOrder> getListByUnionid(String unionid);

    AppAppointmentOrder getOrderByOrderNo(String orderNo);
}
