package com.spup.user.task;


import com.spup.user.controller.AppBatchController;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.data.entity.appointment.AppWorkday;
import com.spup.enums.BatchStatusEnum;
import com.spup.user.enums.OrderCategoryEnum;
import com.spup.user.service.IAppConfigService;
import com.spup.user.service.IAppWorkdayService;
import com.spup.user.service.appointment.IAppBatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;


@Configuration
@EnableScheduling
public class OpenTempExhibitionBatchTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Resource
    private IAppBatchService iAppBatchService;
    @Resource
    private IAppWorkdayService iAppWorkdayService;
    @Resource
    private IAppConfigService iAppConfigService;

    //3.添加定时任务
    @Scheduled(cron = "${scheduler.OpenTempExhibitionBatchTask.cron}")
    protected void configureTasks() { //临展开启的定时任务
        String exhibitionNo = "L20250326151436123";
        logger.info("执行定时开启"+exhibitionNo+"场次");

        byte category = OrderCategoryEnum.EXHIBITION_TEAM.getCode();
        Map<String, Object> configsByGroup = iAppConfigService.getConfigsByGroup("appointment.category." + category+"."+exhibitionNo);

        int offsetDay = Integer.parseInt((String) configsByGroup.get("offset.day"));
        int appointmentDays = Integer.parseInt((String) configsByGroup.get("appointment.days"));

        LocalDate now = LocalDate.now();
        LocalDate start = now.plusDays(offsetDay);
        LocalDate end = now.plusDays(offsetDay).plusDays(appointmentDays);

        while (!start.isAfter(end)){
            String yyyyMMdd = start.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            if(iAppWorkdayService.isWorkDay(yyyyMMdd)){
                Optional<AppWorkday> workDayOpt = iAppWorkdayService.getByDate(yyyyMMdd);
                int ticketStatus = iAppWorkdayService.getDayStatus(workDayOpt.orElse(null), exhibitionNo);
                if(ticketStatus == 1) {
                    Map<String, List<AppBatch>> listByDate = iAppBatchService.getListByDate(category, yyyyMMdd, yyyyMMdd);
                    List<AppBatch> batchs = listByDate.get(yyyyMMdd);
                    for (int i = 0; i < batchs.size(); i++) {
                        AppBatch batch = batchs.get(i);
                        if (batch.getBatchStatus().byteValue() == BatchStatusEnum.CLOSED.getCode()) {
                            batch.setBatchStatus((byte) 1);
                            iAppBatchService.update(batch);
                        }
                    }
                }
            }
            start = start.plusDays(1);
        }
    }

}