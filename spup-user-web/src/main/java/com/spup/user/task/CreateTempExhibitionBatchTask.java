package com.spup.user.task;


import com.spup.user.controller.AppBatchController;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.user.enums.OrderCategoryEnum;
import com.spup.user.service.IAppConfigService;
import com.spup.user.service.appointment.IAppBatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class CreateTempExhibitionBatchTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Autowired
    private IAppBatchService iAppBatchService;
    @Resource
    private IAppConfigService iAppConfigService;
    //3.添加定时任务
    @Scheduled(cron = "${scheduler.CreateTempExhibitionBatchTask.cron}")
    public void configureTasks() {
        logger.info("create L20250326151436123 batch");
        String exhibitionNo = "L20250326151436123";
        byte category = OrderCategoryEnum.EXHIBITION_TEAM.getCode();

        Map<String, Object> configsByGroup = iAppConfigService.getConfigsByGroup("appointment.category." + category+"."+exhibitionNo);
        int offsetDay = Integer.parseInt((String) configsByGroup.get("offset.day"));
        int appointmentDays = Integer.parseInt((String) configsByGroup.get("appointment.days"));

        LocalDate start =  LocalDate.now().plusDays(offsetDay);
        LocalDate end = LocalDate.now().plusDays(offsetDay).plusDays(appointmentDays);

        createTempExhibitionBatch(exhibitionNo,category,start,end);
    }

    public  void createTempExhibitionBatch(String exhibitionNo,byte category,LocalDate start,LocalDate end){
        String[][] times = { { "1000", "1030","*"}};
        int[] ticketTotal = {1,1,1,1,1,1,1};
        while(! start.isAfter(end)) {
            String yyyMMdd = start.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            List<AppBatch> batchList = iAppBatchService.getListByDate(category, yyyMMdd);
            batchList = batchList.stream()
                    .filter(appBatch -> exhibitionNo.equals(appBatch.getBatchRemark()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(batchList)) {
                iAppBatchService.init(start, start, category, times, ticketTotal, exhibitionNo);
            }
            start = start.plusDays(1);
        }
    }
}
