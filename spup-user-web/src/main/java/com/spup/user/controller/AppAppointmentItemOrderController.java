package com.spup.user.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.spup.commons.api.CommonResult;
import com.spup.user.dto.AppAppointmentItemOrderRequest;
import com.spup.user.service.appointment.IAppAppointmentItemOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

@Tag(name = "展项预约")
@RestController
@RequestMapping("/itemOrder")
public class AppAppointmentItemOrderController {
    @Autowired
    private IAppAppointmentItemOrderService iAppAppointmentItemOrderService;

    @Operation(summary = "下单预约")
    @PostMapping(value = "/createOrder")
    public CommonResult<?> createOrder(@RequestBody AppAppointmentItemOrderRequest orderRequest , HttpServletRequest request) throws JsonProcessingException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentItemOrderService.save(orderRequest,unionid);
    }

    @Operation(summary = "预约列表")
    @GetMapping(value = "/list")
    public CommonResult<?> list(HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");
        return iAppAppointmentItemOrderService.getList(unionid);
    }

    @Operation(summary = "取消预约")
    @GetMapping(value = "/cancel/{orderNo}")
    public CommonResult<?> cancel(@PathVariable String orderNo , HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");
        return iAppAppointmentItemOrderService.cancel(orderNo,unionid);
    }

    @Operation(summary = "删除预约记录")
    @GetMapping(value = "/delete/{orderNo}")
    public CommonResult<?> delete(@PathVariable String orderNo , HttpServletRequest request) throws ParseException {
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppAppointmentItemOrderService.delete(orderNo,unionid);
    }
}
