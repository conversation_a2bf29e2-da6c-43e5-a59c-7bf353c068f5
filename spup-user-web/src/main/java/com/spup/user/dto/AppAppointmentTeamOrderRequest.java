package com.spup.user.dto;

import com.spup.enums.OrderCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "团体预约对象")
public class AppAppointmentTeamOrderRequest implements Serializable {
    @Schema(description = "场次编号，非id，如202301180930")
    private String batchNo;
    private Integer visitorsNum;
    private String owerUnit;
    private String owerUnitCode;
    private String ownerName;
    private String ownerPhone;
    @Schema(description = "临展编号，非id，如L20230906T20230923")
    private String exhibitionNo;
    private Byte category = OrderCategoryEnum.TEAM.getCode();

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getVisitorsNum() {
        return visitorsNum;
    }

    public void setVisitorsNum(Integer visitorsNum) {
        this.visitorsNum = visitorsNum;
    }

    public String getOwerUnit() {
        return owerUnit;
    }

    public void setOwerUnit(String owerUnit) {
        this.owerUnit = owerUnit;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerPhone() {
        return ownerPhone;
    }

    public void setOwnerPhone(String ownerPhone) {
        this.ownerPhone = ownerPhone;
    }

    public String getOwerUnitCode() {
        return owerUnitCode;
    }

    public void setOwerUnitCode(String owerUnitCode) {
        this.owerUnitCode = owerUnitCode;
    }

    public String getExhibitionNo() {
        return exhibitionNo;
    }

    public void setExhibitionNo(String exhibitionNo) {
        this.exhibitionNo = exhibitionNo;
    }

    public Byte getCategory() {
        return category;
    }

    public void setCategory(Byte category) {
        this.category = category;
    }
}
