package com.spup.user.dto;

import com.spup.enums.OrderCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "展项预约对象")
public class AppAppointmentItemOrderRequest implements Serializable {
    @Schema(description = "场次编号，非id，如202301180930")
    private String batchNo;
    @Schema(description = "联系人列表，数组字符串格式，如:[{" +
            "  \'idcardCategory\': 1," +
            "  \'idcardNo\': \'string\'," +
            "  \'name\': \'string\'," +
            "  \'phone\': \'string\'" +
            "}]")
    private String contacts;
    @Schema(description = "展项类型(不填默认4为飞阅浦东)")
    private Byte category = OrderCategoryEnum.ITEM_FYPD.getCode();

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public Byte getCategory() {
        return category;
    }

    public void setCategory(Byte category) {
        this.category = category;
    }
}
