package com.spup.user.interceptor;

import com.auth0.jwt.exceptions.AlgorithmMismatchException;
import com.auth0.jwt.exceptions.SignatureVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.spup.commons.api.CommonResult;
import com.spup.commons.api.ResultCodeEnum;
import com.spup.commons.utils.JWTUtil;
import com.spup.user.service.IAppOperateLogService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@Slf4j
@Component
public class TokenInterceptor implements HandlerInterceptor {
    // JWTUtil is now a static utility class - no injection needed
    @Autowired
    private IAppOperateLogService iAppOperateLogService;
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler) throws Exception {
        // return true;
        String token = request.getHeader("Authorization");
        if (request.getMethod().equals(RequestMethod.OPTIONS.name())) {
            log.info("**********options请求");
            return true;
        }
        System.out.println("TokenInterceptor**********" + request.getServerName());
        if (request.getServerName().contains("localhost")) {
            log.info("TokenInterceptor**********本地请求");
            request.getSession().setAttribute("unionid", "ojqzL0-hOlek3HMyLjhvKjTfDnnA");// 默认安然
            return true;
        }
        CommonResult<?> commonResult = null;
        try {
            // 1.校验JWT字符串
            DecodedJWT decodedJWT = JWTUtil.decodeToken(token);
            // 2.取出JWT字符串载荷中的随机token，从Redis中获取用户信息

            Claim unionid = decodedJWT.getClaim("unionid");
            Claim openid = decodedJWT.getClaim("openid");
            System.out.println("TokenInterceptor**********************" + unionid.asString());
            String s_unionid = (String) request.getSession().getAttribute("unionid");
            if (!unionid.asString().equals(s_unionid)) {
                request.getSession().setAttribute("unionid", unionid.asString());
                request.getSession().setAttribute("openid", openid.asString());
            }
            return true;
        } catch (SignatureVerificationException e) {
            System.out.println("无效签名");
            e.printStackTrace();
            commonResult = CommonResult.failed(ResultCodeEnum.GET_TOKEN_KEY_FAILED);
        } catch (TokenExpiredException e) {
            System.out.println("token已经过期");
            e.printStackTrace();
            commonResult = CommonResult.failed(ResultCodeEnum.AUTHORIZED_FAILED);
        } catch (AlgorithmMismatchException e) {
            System.out.println("算法不一致");
            e.printStackTrace();
            commonResult = CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        } catch (Exception e) {
            System.out.println("token无效");
            e.printStackTrace();
            commonResult = CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        }
        try {
            String jsonObjectStr = objectMapper.writeValueAsString(commonResult);
            returnJson(response, jsonObjectStr);
        } catch (Exception ex) {
            log.error("Error writing response in TokenInterceptor", ex);
        }
        return false;
    }

    @Override
    public void postHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler,
            @Nullable ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler,
            @Nullable Exception ex) {
        try {
            log.info("Token Interceptor, after completion");
            iAppOperateLogService.saveLog(request);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void returnJson(HttpServletResponse response, String json) throws Exception {
        // Check if response is already committed to avoid IllegalStateException
        if (response.isCommitted()) {
            log.warn("Response is already committed, cannot write JSON response");
            return;
        }

        PrintWriter writer = null;
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        try {
            writer = response.getWriter();
            writer.print(json);
            writer.flush(); // Ensure data is written
        } catch (IOException e) {
            log.error("Error writing JSON response", e);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (Exception e) {
                    log.error("Error closing writer", e);
                }
            }
        }
    }
}
