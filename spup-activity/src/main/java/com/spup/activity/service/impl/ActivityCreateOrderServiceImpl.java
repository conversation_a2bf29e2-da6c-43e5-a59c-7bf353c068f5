package com.spup.activity.service.impl;

import com.spup.activity.dto.AppAppointmentOrderRequest;
import com.spup.activity.service.ActivityCreateOrderService;
import com.spup.activity.service.ForActivityAppointmentOrderService;
import com.spup.activity.service.ForActivityBatchService;
import com.spup.commons.utils.NumberGenerator;
import com.spup.data.dao.appointment.AppAppointmentOrderDao;
import com.spup.data.dao.appointment.AppAppointmentSuborderDao;
import com.spup.data.entity.activity.ActivityRound;
import com.spup.data.entity.activity.ActivitySubmitCustomer;
import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.data.entity.appointment.AppAppointmentSuborder;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.enums.BatchCategoryEnum;
import com.spup.enums.OrderStatusEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Service
public class ActivityCreateOrderServiceImpl implements ActivityCreateOrderService {
    @Resource
    private ForActivityBatchService iAppBatchService;
    @Resource
    private ForActivityAppointmentOrderService iAppAppointmentOrderService;
    @Resource
    private AppAppointmentOrderDao appAppointmentOrderDao;
    @Resource
    private AppAppointmentSuborderDao appAppointmentSuborderDao;

    public String  saveFromActivity(ActivityRound actRound, List<ActivitySubmitCustomer> customerList, String unionid)  {
        AppAppointmentOrderRequest orderRequest = new AppAppointmentOrderRequest();

        LocalDateTime startDateTime = actRound.getActRoundStartDateTime();
        String yyyyMMdd = startDateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String hhmm = startDateTime.format(DateTimeFormatter.ofPattern("HHmm"));

        List<AppBatch> batchList = iAppBatchService.getListByDate(BatchCategoryEnum.TICKET.getCode(), yyyyMMdd);

        Optional<AppBatch> firstBatch = batchList.stream()
                .filter(batch ->
                        batch.getBatchStartTime().compareTo(hhmm) <= 0
                                && batch.getBatchEndTime().compareTo(hhmm) > 0)
                .findFirst();
        if(!firstBatch.isPresent()){
            throw new RuntimeException("未找到匹配的场次");
        }

        String batchNo = firstBatch.get().getBatchNo();
        //场次校验
        AppBatch batch = iAppBatchService.getByNo(batchNo, (byte)1);
        if(batch==null){
            throw new RuntimeException("场次不正确");
        }
        int personNum = customerList.size();

        /**
         * 数据处理
         */
        DecimalFormat df = new DecimalFormat("00");

        String orderNo = NumberGenerator.getOrderNo();
        AppAppointmentOrder order = new AppAppointmentOrder();
        order.setOrderNo(orderNo);
        order.setBatchNo(batch.getBatchNo());
        order.setBatchDate(batch.getBatchDate());
        order.setBatchStartTime(batch.getBatchStartTime());
        order.setBatchEndTime(batch.getBatchEndTime());
        order.setOwnerUnionid(unionid);
        order.setOrderStatus(OrderStatusEnum.SUCCESS.getCode());
        order.setOrderRemark(orderRequest.getExhibitionNo());
        order.setOrderCategory(orderRequest.getCategory());

        appAppointmentOrderDao.save(order);

        //iAppBatchService.updateRemaining(batchNo,(byte)1,-1*personNum);
        for(int i=0;i<personNum;i++){
            ActivitySubmitCustomer customer = customerList.get(i);
            AppAppointmentSuborder suborder = new AppAppointmentSuborder();
            suborder.setOrderNo(order.getOrderNo());
            suborder.setSuborderNo(order.getOrderNo()+df.format((i+1)));

            suborder.setBatchNo(order.getBatchNo());
            suborder.setBatchDate(order.getBatchDate());
            suborder.setBatchStartTimeStr(order.getBatchStartTime());
            suborder.setBatchEndTimeStr(order.getBatchEndTime());

            suborder.setOnwerUnionid(order.getOwnerUnionid());
            suborder.setContactsName(customer.getUsername());
            suborder.setContactsPhone(customer.getPhoneString());
            suborder.setContactsIdcardCategory((byte)(customer.getPassType() == ActivitySubmitCustomer.SubmitCustomerPassTypeEnum.IDCARD?1:2));
            suborder.setContactsIdcardNo(customer.getPassString());

            suborder.setSuborderStatus(OrderStatusEnum.SUCCESS.getCode());

            appAppointmentSuborderDao.save(suborder);
        }
        return orderNo;
    }

    @Override
    public void cancelFromActivity(String orderNo, String unionid) {
        iAppAppointmentOrderService.cancel(orderNo,unionid);
    }
}
