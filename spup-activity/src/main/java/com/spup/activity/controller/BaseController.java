package com.spup.activity.controller;


import com.spup.activity.service.ActivityRoundService;
import com.spup.data.entity.activity.Activity;
import com.spup.data.entity.activity.ActivityRound;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "base controller")
@RestController
@RequestMapping(value="/")
public abstract class BaseController {
    @Resource(name = "ActivityRoundServiceImpl")
    private ActivityRoundService actRoundService;

    protected void updateActivStatus(List<Activity> activList) {
        actRoundService.updateActivStatus(activList);
    }

    protected void updateActRoundStatus(List<ActivityRound> actRoundList) {
       actRoundService.updateActRoundStatus(actRoundList);
    }

    protected Activity.ActStatusEnum getActStatus(String activityId) {
        return actRoundService.getFreshedActStatus(activityId);
    }

    protected ActivityRound.ActRoundStatusEnum getActRoundStatus(String actRoundId) {
        return actRoundService.getFreshActRoundStatus(actRoundId);
    }
}



