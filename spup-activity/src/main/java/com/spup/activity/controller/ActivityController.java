package com.spup.activity.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.spup.activity.dto.ActivityDTO;
import com.spup.activity.dto.ActivityRoundCheckinDTO;
import com.spup.activity.dto.ActivityRoundDTO;
import com.spup.activity.dto.ActivityRoundVo;
import com.spup.activity.service.ActivityRoundService;
import com.spup.activity.service.impl.ActivityRoundServiceChildImpl;
import com.spup.activity.service.impl.ActivityRoundServiceNoLimitImpl;
import com.spup.activity.service.impl.ActivityRoundServiceNormalImpl;
import com.spup.commons.api.CommonResult;
import com.spup.data.dao.activity.ActivityDao;
import com.spup.data.dao.activity.ActivityRoundDao;
import com.spup.data.dao.activity.ActivitySubmitCustomerDao;
import com.spup.data.entity.activity.Activity;
import com.spup.data.entity.activity.ActivityRound;
import com.spup.data.entity.activity.ActivitySubmitCustomer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.UnavailableException;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Tag(name = "报名" )
@RestController
@RequestMapping(value = "/app")
public class ActivityController extends BaseController {
    @Resource
    ApplicationContext context;
    @Resource
    private ActivityDao actDao;
    @Resource
    private ActivityRoundDao actRoundDao;
    @Resource
    private ActivitySubmitCustomerDao actCustomerDao;
    @Resource(name = "ActivityRoundServiceImpl")
    private ActivityRoundService actRoundService;

    @Operation(summary = "获取活动列表")
    @GetMapping(value="/activity/list")
    public CommonResult<List<Activity>> getActivityList () {
        List<Activity> activList = (List<Activity>)actDao.findAll();
        updateActivStatus(activList);
        List<Activity> result = activList.stream()
                .filter(act -> act.getStatus() != Activity.ActStatusEnum.DEPRECATED)
                .sorted((act1, act2) -> (int) (act1.getId() - act2.getId()))
                .collect(Collectors.toList());
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "获取活动详情")
    @GetMapping(value="/activity/detail/{activityId}")
    public CommonResult<?> getActivityDetail (@PathVariable String activityId) {
        Optional<Activity> actOpt = actDao.getByActivityId(activityId);
        if (!actOpt.isPresent()) {
            return CommonResult.failed("activity not exist");
        }
        Activity activity = actOpt.get();
        updateActivStatus(Collections.singletonList(activity));

        if(activity.getStatus() == Activity.ActStatusEnum.DEPRECATED){
            return CommonResult.failed("activity has deprecated");
        }

        ActivityDTO activityDTO = new ActivityDTO();
        activityDTO.setActivity(activity);
        List<ActivityRound> roundList = actRoundDao.findByActivityId(activity.getActivityId());
        updateActRoundStatus(roundList);
        activityDTO.setRounds(roundList);
        return CommonResult.succeeded(activityDTO);
    }

    @Operation(summary = "报名提交")
    @PostMapping(value="/activityRound/submit/{activityRoundId}")
    public CommonResult<?> acitivityRoundSubmit (@PathVariable String activityRoundId,
                                                 @RequestBody List<ActivitySubmitCustomer> activityCustomerList,
                                                 HttpServletRequest req) throws UnavailableException, JsonProcessingException {
        String unionid = (String) req.getSession().getAttribute("unionid");
        if (activityCustomerList.isEmpty()) {
            return CommonResult.failed("报名用户不能为空");
        }
        String actRoundId = activityRoundId;
        if (!activityCustomerList.stream().allMatch( customer -> actRoundId.equals(customer.getActRoundId()))) {
            return CommonResult.failed("报名场次信息不匹配");
        }

        Optional<ActivityRound> actRoundOpt = actRoundDao.findByActRoundId(actRoundId);
        if (!actRoundOpt.isPresent()) {
            return CommonResult.failed("活动场次不存在");
        }
        ActivityRound actRound = actRoundOpt.get();
        if (actRound.getStatus() != ActivityRound.ActRoundStatusEnum.SUBMITTING) {
            return CommonResult.failed("报名不在进行中");
        }
        Optional<Activity> activityOpt = actDao.getByActivityId(actRound.getActivityId());
        if(!activityOpt.isPresent()){
            return CommonResult.failed("活动不存在");
        }
        Activity activity = activityOpt.get();
        if (activity.getStatus() == Activity.ActStatusEnum.CLOSED
                || activity.getStatus() == Activity.ActStatusEnum.DEPRECATED) {
            return CommonResult.failed("活动已下线");
        }
        getRoundService(actRound);
        return actRoundService.submit(activityCustomerList, unionid, actRound);
    }

    @Operation(summary = "获取某活动动场次信息及该用户是否报名信息")
    @GetMapping(value="/activityRound/{activityId}")
    public CommonResult<?> getActivityRoundAndCustomerInfo (@PathVariable String activityId, HttpServletRequest req)  {

        String unionid = (String)req.getSession().getAttribute("unionid");
        Optional<Activity> actOpt = actDao.getByActivityId(activityId);
        List<ActivityRound> roundList = actRoundDao.findByActivityId(activityId);
        if(!actOpt.isPresent()){
            return CommonResult.failed("活动不存在");
        }
        updateActivStatus(Collections.singletonList(actOpt.get()));
        updateActRoundStatus(roundList);

        List<ActivityRoundDTO> result = roundList.stream()
                .filter(round -> round.getStatus()!= ActivityRound.ActRoundStatusEnum.DEPRECATED)
                .map(round -> {
                    ActivityRoundVo vo = new ActivityRoundVo();
                    vo.setActRound(round);

                    ActivityRoundDTO dto = new ActivityRoundDTO();
                    dto.setRoundVo(vo);

                    if (round.getType() == ActivityRound.ActRoundTypeEnum.NORMAL) {
                        actRoundService = context.getBean(ActivityRoundServiceNormalImpl.class);
                    }
                    if (round.getType() == ActivityRound.ActRoundTypeEnum.CHILD) {
                        actRoundService = context.getBean(ActivityRoundServiceChildImpl.class);
                    }
                    if (round.getType() == ActivityRound.ActRoundTypeEnum.SPECIAL) {
                        return null;
                    }

                    List<ActivitySubmitCustomer> subRecordList = actCustomerDao.findActivitySubmitCustomerByActRoundIdAndUnionid(round.getActRoundId(), unionid);
                    subRecordList = subRecordList.stream()
                            .filter(subRecord -> subRecord.getStatus() != ActivitySubmitCustomer.SubmitCustomerStatusEnum.CANCELLED)
                            .collect(Collectors.toList());

                    dto.setSubmitCustomers(subRecordList);
                    return dto;
                }).collect(Collectors.toList());
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "可签到场次")
    @GetMapping(value="/activityRound/checkin/list")
    public CommonResult<?> checkinList(HttpServletRequest req)  {
        String unionid = (String)req.getSession().getAttribute("unionid");
        List<ActivityRoundCheckinDTO> result = actRoundService.getActRoundCheckInDto(unionid);
        return CommonResult.succeeded(result);
    }


    @Operation(summary = "签到")
    @GetMapping(value="/activityRound/checkin/{actRoundId}")
    public CommonResult<?> checkin (@PathVariable String actRoundId, HttpServletRequest req) throws UnavailableException  {
        String unionid = (String)req.getSession().getAttribute("unionid");
        Optional<ActivityRound> actRoundOpt = actRoundDao.findByActRoundId(actRoundId);
        if (!actRoundOpt.isPresent()) {
            return CommonResult.failed("活动场次不存在");
        }
        ActivityRound actRound = actRoundOpt.get();
        getRoundService(actRound);
        return actRoundService.checkInAction(actRoundId, unionid);

    }

    @Operation(summary = "我报名的活动列表")
    @GetMapping(value="/activityRound/mySubmitted")
    public CommonResult<?> mySubmitted (HttpServletRequest req) {
        String unionid = (String)req.getSession().getAttribute("unionid");
        List<ActivitySubmitCustomer> subRecordList = actCustomerDao.findByUnionid(unionid);
        List<String> roundIdList = subRecordList.stream()
                .filter(subRecord -> subRecord.getStatus() != ActivitySubmitCustomer.SubmitCustomerStatusEnum.CANCELLED)
                .map(ActivitySubmitCustomer::getActRoundId)
                .distinct()
                .collect(Collectors.toList());
        List<ActivityRoundDTO> result;
        result = roundIdList.stream()
                .map( roundId -> {
                    Optional<ActivityRound> actRoundOpt =
                            actRoundDao.findByActRoundId(roundId);
                    if (!actRoundOpt.isPresent()) {
                        return null;
                    }
                    List<ActivitySubmitCustomer> submitCustomers = actCustomerDao.findActivitySubmitCustomerByActRoundIdAndUnionid(roundId, unionid);
                    submitCustomers = submitCustomers.stream()
                            .filter(subRecord -> subRecord.getStatus() != ActivitySubmitCustomer.SubmitCustomerStatusEnum.CANCELLED)
                            .collect(Collectors.toList());

                    ActivityRoundDTO dto = new ActivityRoundDTO();
                    ActivityRound actRound = actRoundOpt.get();
                    updateActRoundStatus(Collections.singletonList(actRound));
                    Optional<Activity> activityOptional = actDao.getByActivityId(actRound.getActivityId());
                    if(!activityOptional.isPresent()){
                        return null;
                    }
                    Activity activity = activityOptional.get();
                    dto.setActivityId(activity.getActivityId());
                    dto.setActivityName(activity.getActivityName());
                    ActivityRoundVo vo = new ActivityRoundVo();
                    vo.setActRound(actRound);

                    dto.setRoundVo(vo);
                    dto.setSubmitCustomers(submitCustomers);
                    return dto;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        //result = result.stream().collect(Collectors.toList());
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "获取活动报名情况")
    @GetMapping({"/activity/submit/list/{activityId}"})
    public CommonResult<?> submitList (@PathVariable String activityId) {
        Optional<Activity> actOpt = this.actDao.getByActivityId(activityId);
        List<ActivityRound> roundList = this.actRoundDao.findByActivityId(activityId);
        if (!actOpt.isPresent()) {
            return CommonResult.failed("活动不存在");
        } else {
            this.updateActivStatus(Collections.singletonList(actOpt.get()));
            this.updateActRoundStatus(roundList);
            List<ActivitySubmitCustomer> customerList = new ArrayList<>();
            roundList.forEach((round) -> {
                List<ActivitySubmitCustomer> subCustomerList = this.actCustomerDao.findByActRoundId(round.getActRoundId());
                subCustomerList =
                        subCustomerList.stream()
                                .filter(subCustomer -> subCustomer.getStatus() != ActivitySubmitCustomer.SubmitCustomerStatusEnum.CANCELLED)
                                .collect(Collectors.toList());
                customerList.addAll(subCustomerList);
            });
            return CommonResult.succeeded(customerList);
        }
    }

    @Operation(summary = "取消预约")
    @GetMapping(value="/activityRound/cancel/{actRoundId}")
    public CommonResult<?> cancelSubmit (@PathVariable String actRoundId, HttpServletRequest req) throws UnavailableException, JsonProcessingException {

        Optional<ActivityRound> actRoundOptional = actRoundDao.findByActRoundId(actRoundId);
        if(!actRoundOptional.isPresent()){
            return CommonResult.failed("场次不存在");
        }
        ActivityRound actRound = actRoundOptional.get();
        String unionid = (String)req.getSession().getAttribute("unionid");

        List<ActivitySubmitCustomer> subRecordList =
                actCustomerDao.findActivitySubmitCustomerByActRoundIdAndUnionid(actRoundId, unionid);

        //getRoundService(actRound);

        return actRoundService.cancelActRound(subRecordList, actRound);
    }

    private void getRoundService(ActivityRound actRound) throws UnavailableException {
        System.out.println("test round type:"+actRound.getType());
        if (actRound.getType() == ActivityRound.ActRoundTypeEnum.NORMAL) {
            actRoundService = context.getBean(ActivityRoundServiceNormalImpl.class);
        }
        if (actRound.getType() == ActivityRound.ActRoundTypeEnum.CHILD) {
            actRoundService = context.getBean(ActivityRoundServiceChildImpl.class);
        }
        if (actRound.getType() == ActivityRound.ActRoundTypeEnum.NOLIMIT) {
            actRoundService = context.getBean(ActivityRoundServiceNoLimitImpl.class);
        }
        if (actRound.getType() == ActivityRound.ActRoundTypeEnum.SPECIAL) {
            throw new UnavailableException(actRound.getType().toString());
        }
    }
}
