{"java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "automatic", "java.project.sourcePaths": ["spup-user-web/src/main/java", "spup-admin-web/src/main/java", "common/src/main/java", "app-db/src/main/java", "activity/src/main/java"], "java.project.outputPath": "target/classes", "java.project.referencedLibraries": ["lib/**/*.jar"], "java.debug.settings.onBuildFailureProceed": true, "java.debug.settings.console": "integratedTerminal", "java.debug.settings.showQualifiedNames": true, "java.debug.settings.showStaticVariables": true, "java.debug.settings.showHex": false, "java.debug.settings.maxStringLength": 0, "java.debug.settings.enableHotCodeReplace": true, "java.debug.settings.hotCodeReplace": "auto", "java.debug.settings.exceptionBreakpoint.skipClasses": ["org.springframework.boot.devtools.restart.SilentExitExceptionHandler$SilentExitException"], "java.autobuild.enabled": true, "java.import.maven.enabled": true, "java.import.gradle.enabled": false, "java.sources.organizeImports.starThreshold": 99, "java.sources.organizeImports.staticStarThreshold": 99}