package com.spup.core.service;

import com.spup.data.entity.AppSurroundingGoods;

/**
 * Base Surrounding Goods Service Interface
 * Common goods operations that can be implemented by specific modules
 */
public interface BaseSurroundingGoodsService {

    /**
     * View goods by id
     */
    AppSurroundingGoods view(long id);

    /**
     * Create new goods (admin operation)
     */
    AppSurroundingGoods create(AppSurroundingGoods goods, String operatorId);

    /**
     * Update goods (admin operation)
     */
    AppSurroundingGoods update(AppSurroundingGoods goods, String operatorId);

    /**
     * Delete goods (admin operation)
     */
    int delete(long id, String operatorId);
}
