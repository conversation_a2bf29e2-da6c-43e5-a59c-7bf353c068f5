package com.spup.core.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.web.bind.annotation.ModelAttribute;

import com.spup.commons.api.CommonResult;

import lombok.extern.slf4j.Slf4j;

/**
 * Base Controller
 * Provides common functionality for all controllers
 * This unifies controller functionality across all modules
 */
@Slf4j
public abstract class BaseController {

    protected HttpServletRequest request;
    protected HttpSession session;

    @ModelAttribute
    public void setRequestAndSession(HttpServletRequest request) {
        this.request = request;
        this.session = request.getSession();
    }

    /**
     * Get current user's unionid from session
     */
    protected String getCurrentUnionid() {
        Object unionid = session.getAttribute("unionid");
        if (unionid == null) {
            log.warn("No unionid found in session");
            return null;
        }
        return unionid.toString();
    }

    /**
     * Require authentication and return unionid
     * Throws exception if not authenticated
     */
    protected String requireAuthentication() {
        String unionid = getCurrentUnionid();
        if (unionid == null) {
            throw new RuntimeException("Authentication required");
        }
        return unionid;
    }

    /**
     * Log request with parameters for debugging
     */
    protected void logRequest(String operation, Object params) {
        if (log.isDebugEnabled()) {
            log.debug("Operation: {}, Params: {}, User: {}",
                operation, params, getCurrentUnionid());
        }
    }

    /**
     * Create success response
     */
    protected <T> CommonResult<T> success(T data) {
        return CommonResult.succeeded(data);
    }

    /**
     * Create success response with message
     */
    protected <T> CommonResult<T> success(T data, String message) {
        return CommonResult.succeeded(data, message);
    }

    /**
     * Create failed response
     */
    protected <T> CommonResult<T> failed(String message) {
        return CommonResult.failed(message);
    }

    /**
     * Get client IP address
     */
    protected String getClientIpAddress() {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * Get user agent
     */
    protected String getUserAgent() {
        return request.getHeader("User-Agent");
    }

    /**
     * Get current user's openid from session
     */
    protected String getCurrentOpenid() {
        Object openid = session.getAttribute("openid");
        if (openid == null) {
            log.warn("No openid found in session");
            return null;
        }
        return openid.toString();
    }

    /**
     * Check if user is authenticated
     */
    protected boolean isAuthenticated() {
        return getCurrentUnionid() != null;
    }

    /**
     * Create failed response with data
     */
    protected <T> CommonResult<T> failed(String message, T data) {
        return CommonResult.failed(message);
    }

    /**
     * Log request information
     */
    protected void logRequest(String operation) {
        log.info("=== {} ===", operation);
        log.info("Request URL: {}", request.getRequestURL());
        log.info("Request Method: {}", request.getMethod());
        log.info("User unionid: {}", getCurrentUnionid());
    }

    // Activity-specific methods (to be overridden by activity controllers)
    protected void updateActivStatus(java.util.List<?> activList) {
        // Default implementation - can be overridden by activity controllers
        log.debug("updateActivStatus called with {} items", activList.size());
    }

    protected void updateActRoundStatus(java.util.List<?> actRoundList) {
        // Default implementation - can be overridden by activity controllers
        log.debug("updateActRoundStatus called with {} items", actRoundList.size());
    }

    protected Object getActStatus(String activityId) {
        // Default implementation - can be overridden by activity controllers
        log.debug("getActStatus called for activityId: {}", activityId);
        return null;
    }

    protected Object getActRoundStatus(String actRoundId) {
        // Default implementation - can be overridden by activity controllers
        log.debug("getActRoundStatus called for actRoundId: {}", actRoundId);
        return null;
    }
}
