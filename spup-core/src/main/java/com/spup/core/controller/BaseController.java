package com.spup.core.controller;

import com.spup.commons.api.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ModelAttribute;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/**
 * Base Controller
 * Provides common functionality for all controllers
 */
@Slf4j
public abstract class BaseController {

    protected HttpServletRequest request;
    protected HttpSession session;

    @ModelAttribute
    public void setRequestAndSession(HttpServletRequest request) {
        this.request = request;
        this.session = request.getSession();
    }

    /**
     * Get current user's unionid from session
     */
    protected String getCurrentUnionid() {
        Object unionid = session.getAttribute("unionid");
        if (unionid == null) {
            log.warn("No unionid found in session");
            return null;
        }
        return unionid.toString();
    }

    /**
     * Get current user's openid from session
     */
    protected String getCurrentOpenid() {
        Object openid = session.getAttribute("openid");
        if (openid == null) {
            log.warn("No openid found in session");
            return null;
        }
        return openid.toString();
    }

    /**
     * Check if user is authenticated
     */
    protected boolean isAuthenticated() {
        return getCurrentUnionid() != null;
    }

    /**
     * Require authentication and return unionid
     */
    protected String requireAuthentication() {
        String unionid = getCurrentUnionid();
        if (unionid == null) {
            throw new IllegalStateException("User not authenticated");
        }
        return unionid;
    }

    /**
     * Create success response
     */
    protected <T> CommonResult<T> success(T data) {
        return CommonResult.succeeded(data);
    }

    /**
     * Create success response with message
     */
    protected <T> CommonResult<T> success(T data, String message) {
        return CommonResult.succeeded(data, message);
    }

    /**
     * Create failed response
     */
    protected <T> CommonResult<T> failed(String message) {
        return CommonResult.failed(message);
    }

    /**
     * Create failed response with data
     */
    protected <T> CommonResult<T> failed(String message, T data) {
        return CommonResult.failed(message);
    }

    /**
     * Log request information
     */
    protected void logRequest(String operation) {
        log.info("=== {} ===", operation);
        log.info("Request URL: {}", request.getRequestURL());
        log.info("Request Method: {}", request.getMethod());
        log.info("User unionid: {}", getCurrentUnionid());
    }

    /**
     * Log request with parameters
     */
    protected void logRequest(String operation, Object params) {
        logRequest(operation);
        log.info("Request params: {}", params);
    }
}
