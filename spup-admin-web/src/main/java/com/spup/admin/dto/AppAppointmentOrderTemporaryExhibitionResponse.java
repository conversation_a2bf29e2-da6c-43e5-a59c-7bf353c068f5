package com.spup.admin.dto;


import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.data.entity.appointment.AppAppointmentSuborderTemporaryExhibition;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "预约下单对象")
public class AppAppointmentOrderTemporaryExhibitionResponse extends AppAppointmentOrder {
    private List<AppAppointmentSuborderTemporaryExhibition> suborders;

    public List<AppAppointmentSuborderTemporaryExhibition> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppAppointmentSuborderTemporaryExhibition> suborders) {
        this.suborders = suborders;
    }
}