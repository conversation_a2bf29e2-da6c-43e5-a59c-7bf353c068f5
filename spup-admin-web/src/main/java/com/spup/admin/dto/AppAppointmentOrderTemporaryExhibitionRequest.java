package com.spup.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "预约下单对象")
public class AppAppointmentOrderTemporaryExhibitionRequest implements Serializable {
    @Schema(description = "场次编号，非id，如202301180930")
    private String batchNo;

    @Schema(description = "临展编号，非id，如L20230906T20230923")
    private String exhibitionNo;

    @Schema(description = "联系人列表，数组字符串格式",
            example = "[{\"idcardCategory\": 1, \"idcardNo\": \"string\", \"name\": \"string\", \"phone\": \"string\"}]")
    private String contacts;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getExhibitionNo() {
        return exhibitionNo;
    }

    public void setExhibitionNo(String exhibitionNo) {
        this.exhibitionNo = exhibitionNo;
    }
}
