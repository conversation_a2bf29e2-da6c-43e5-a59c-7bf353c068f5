package com.spup.admin.service.appointment;

import com.spup.commons.api.CommonResult;
import com.spup.admin.dto.AppAppointmentOrderTemporaryExhibitionRequest;
import com.spup.admin.service.IOrderService;

import java.io.IOException;

public interface IAppAppointmentOrderTemporaryExhibitionService extends IOrderService {
    // 对外接口
    CommonResult<?> save(AppAppointmentOrderTemporaryExhibitionRequest orderRequest, String unionid) throws IOException;

    CommonResult<?> delete(String orderNo);
    // 内部接口
}
