package com.spup.admin.service.impl;


import com.spup.data.dao.sys.AppConfigDao;
import com.spup.data.entity.sys.AppConfig;
import com.spup.admin.service.IAppConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AppConfigServiceImpl implements IAppConfigService {
    @Resource
    private AppConfigDao appConfigDao;
    @Override
    public  Map<String,Object> getConfigsByGroup(String groupNo) {
        List<AppConfig> appConfigs = appConfigDao.findByGroupNo(groupNo);
        Map<String,Object> result = new HashMap<>();
        for (AppConfig config : appConfigs ) {
            result.put(config.getRuleName(),config.getRuleValue());
        }
        return result;
    }
}
