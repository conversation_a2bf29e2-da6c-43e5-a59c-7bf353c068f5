package com.spup.admin.service.appointment.impl;

import com.spup.data.dao.appointment.AppAppointmentInstructionsDao;
import com.spup.data.entity.appointment.AppAppointmentInstructions;
import com.spup.admin.service.appointment.IAppAppointmentInstructionsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class AppAppointmentInstructionsServiceImpl implements IAppAppointmentInstructionsService {
    @Resource
    private AppAppointmentInstructionsDao appAppointmentInstructionsDao;

    public AppAppointmentInstructions update(String audienceNotice, String visitingInstructions, String admissionNotice, String unionid){
        AppAppointmentInstructions entity = get();
        entity.setAudienceNotice(audienceNotice);
        entity.setVisitingInstructions(visitingInstructions);
        entity.setAdmissionNotice(admissionNotice);

        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateBy(unionid);

        return appAppointmentInstructionsDao.save(entity);
    }

    public AppAppointmentInstructions get(){
        Optional<AppAppointmentInstructions> byId = appAppointmentInstructionsDao.findById(1L);
        if(byId.isPresent()){
            return byId.get();
        }
        return null;
    }
}
