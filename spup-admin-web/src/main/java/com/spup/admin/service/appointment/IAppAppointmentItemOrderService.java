package com.spup.admin.service.appointment;

import com.spup.commons.api.CommonResult;
import com.spup.admin.dto.AppAppointmentItemOrderRequest;
import com.spup.admin.service.IOrderService;

import java.io.IOException;


public interface IAppAppointmentItemOrderService extends IOrderService {
    //对外接口
    public CommonResult<?> save(AppAppointmentItemOrderRequest orderRequest, String unionid) throws IOException;
    public CommonResult<?> getList(String unionid);
    public CommonResult<?> cancel(String orderNo,String unionid);
    public CommonResult<?> delete(String orderNo,String unionid);
    public CommonResult<?> breaked(String orderNo,String unionid);

}
