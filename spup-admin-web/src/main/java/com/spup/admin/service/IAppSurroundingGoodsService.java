package com.spup.admin.service;

import com.spup.data.entity.AppSurroundingGoods;
import com.spup.admin.dto.GoodsListRequest;
import org.springframework.data.domain.Page;

public interface IAppSurroundingGoodsService {
    Page<AppSurroundingGoods> getListByPage(GoodsListRequest listParam);
    AppSurroundingGoods view(long id);
    AppSurroundingGoods create(AppSurroundingGoods goods,String unionid);
    AppSurroundingGoods update(AppSurroundingGoods appSurroundingGoods,String openId);
    int delete(long id, String openId);
}
