package com.spup.admin.controller.others;

import com.spup.commons.api.CommonResult;
import com.spup.admin.service.appointment.IAppAppointmentInstructionsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.Map;

@Tag(name = "参观须知等更新")
@RestController
@RequestMapping(value = "/instructions")
public class AppInstructionsController {
    @Resource
    private IAppAppointmentInstructionsService iAppAppointmentInstructionsService;

    @Operation(summary = "查询")
    @GetMapping(value = "/get")
    public CommonResult<?> get() throws UnsupportedEncodingException {
        return CommonResult.succeeded(iAppAppointmentInstructionsService.get());
    }

    @Operation(summary = "更新")
    @PostMapping(value = "/update")
    public CommonResult<?> update(@RequestBody Map<String, String> map, HttpServletRequest req)
            throws UnsupportedEncodingException {
        String audienceNotice = (String) map.get("audienceNotice");
        String visitingInstructions = (String) map.get("visitingInstructions");
        String admissionNotice = (String) map.get("admissionNotice");

        String unionid = (String) req.getSession().getAttribute("unionid");
        return CommonResult.succeeded(iAppAppointmentInstructionsService.update(audienceNotice, visitingInstructions,
                admissionNotice, unionid));
    }
}
