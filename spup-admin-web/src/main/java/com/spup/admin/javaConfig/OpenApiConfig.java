package com.spup.admin.javaConfig;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * SpringDoc OpenAPI Configuration
 * Replaces the old Springfox Swagger2 configuration
 */
@Configuration
@Profile("dev")
public class OpenApiConfig {

    /**
     * OpenAPI Configuration Bean
     * Replaces the old Docket configuration
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .addSecurityItem(new SecurityRequirement().addList("Authorization"))
                .components(new Components()
                        .addSecuritySchemes("Authorization",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("令牌")
                        )
                );
    }

    /**
     * API Information
     * Replaces the old ApiInfo configuration
     */
    private Info apiInfo() {
        return new Info()
                .title("浦东规划馆后台管理API")
                .description("接口文档详情信息 - 基于SpringDoc OpenAPI 3")
                .version("2.0");
    }
}