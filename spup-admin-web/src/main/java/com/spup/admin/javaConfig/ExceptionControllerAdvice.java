package com.spup.admin.javaConfig;

import com.spup.commons.api.CommonResult;
import com.spup.core.config.BaseExceptionHandler;
import com.spup.core.exception.ValidException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@RestControllerAdvice(basePackages="com.spup")
public class ExceptionControllerAdvice extends BaseExceptionHandler {

    /**
     * Handle admin-specific ValidException
     */
    @ExceptionHandler(value = ValidException.class)
    public CommonResult<?> handleAdminValidException(ValidException e, HttpServletRequest request) {
        try {
            log.error("=== ADMIN VALIDATION EXCEPTION ===");
            log.error("Request URL: {}", request.getRequestURL());
            log.error("Request Method: {}", request.getMethod());
            log.error("Admin validation error:", e);

            return CommonResult.failed(e.getMessage());
        } catch (Exception handlerException) {
            log.error("Exception in admin validation handler:", handlerException);
            return createSafeErrorResponse("Admin validation failed");
        }
    }

    /**
     * Handle admin-specific exceptions
     */
    @Override
    protected CommonResult<?> handleModuleSpecificException(Exception e,
                                                           HttpServletRequest request,
                                                           HttpServletResponse response) {
        // Admin-specific exception handling logic
        log.error("Admin-specific exception handling for: {}", e.getClass().getSimpleName());

        // Add admin-specific exception types here
        if (e instanceof SecurityException) {
            return CommonResult.failed("Admin security error: " + e.getMessage());
        }

        // Default admin error response
        return CommonResult.failed("Admin system error");
    }

}
