package com.spup.admin.enums;

public enum ItemOrderStatusEnum {
    PROGRESSING((short)1, "预约中"),
    SUCCESS((short)2, "预约成功"),
    FINISHED((short)4, "已完成"),
    CANCELED((short)8, "已取消"),
    ; //此写法防止扩充时忘记分号

    private short code;
    @SuppressWarnings("unused")
    private String name;

    private ItemOrderStatusEnum(short code, String name) {
        this.code = code;
        this.name = name;
    }

    public short getCode() {
        return code;
    }

    public void setCode(short code) {
        this.code = code;
    }

    public static void main(String[] args) {
        byte a = 1;
        System.out.println(a&-1);
    }
}
